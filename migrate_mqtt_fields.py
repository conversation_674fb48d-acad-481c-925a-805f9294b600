#!/usr/bin/env python3
"""
数据库迁移脚本：添加MQTT相关字段和表
"""

import sqlite3
import os

def migrate_database():
    db_path = 'dev-db.sqlite'
    
    if not os.path.exists(db_path):
        print(f"数据库文件 {db_path} 不存在")
        return
    
    conn = sqlite3.connect(db_path)
    cursor = conn.cursor()
    
    try:
        # 检查devices表是否已有MQTT字段
        cursor.execute("PRAGMA table_info(devices)")
        columns = [column[1] for column in cursor.fetchall()]
        
        mqtt_fields = [
            'mqtt_broker_host',
            'mqtt_broker_port', 
            'mqtt_username',
            'mqtt_password',
            'mqtt_client_id',
            'mqtt_subscribe_topics',
            'mqtt_monitoring_enabled'
        ]
        
        # 添加缺失的MQTT字段
        for field in mqtt_fields:
            if field not in columns:
                if field == 'mqtt_broker_port':
                    cursor.execute(f"ALTER TABLE devices ADD COLUMN {field} INTEGER DEFAULT 1883")
                elif field == 'mqtt_monitoring_enabled':
                    cursor.execute(f"ALTER TABLE devices ADD COLUMN {field} BOOLEAN DEFAULT 0")
                else:
                    cursor.execute(f"ALTER TABLE devices ADD COLUMN {field} TEXT")
                print(f"添加字段: {field}")
        
        # 检查mqtt_logs表是否存在
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='mqtt_logs'")
        if not cursor.fetchone():
            # 创建mqtt_logs表
            cursor.execute("""
                CREATE TABLE mqtt_logs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    device_id VARCHAR(36) NOT NULL,
                    topic VARCHAR(255) NOT NULL,
                    payload TEXT,
                    direction VARCHAR(10) NOT NULL,
                    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (device_id) REFERENCES devices (internal_device_id)
                )
            """)
            print("创建mqtt_logs表")
        
        conn.commit()
        print("数据库迁移完成！")
        
    except Exception as e:
        print(f"迁移失败: {e}")
        conn.rollback()
    finally:
        conn.close()

if __name__ == '__main__':
    migrate_database()
